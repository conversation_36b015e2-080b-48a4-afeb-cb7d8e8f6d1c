import { Phone, CheckCircle, Clock, Shield, Users, Star } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';

const Home = () => {
  const services = [
    {
      title: 'Emergency Plumbing',
      description: '24/7 emergency response for burst pipes, severe leaks, and urgent repairs.',
      icon: '🚨'
    },
    {
      title: 'Water Heater Services',
      description: 'Installation, repair, and maintenance of traditional and tankless water heaters.',
      icon: '🔥'
    },
    {
      title: 'Drain Cleaning',
      description: 'Professional drain cleaning and sewer camera inspection services.',
      icon: '🚿'
    },
    {
      title: 'Pipe Repair & Installation',
      description: 'Complete pipe repair, replacement, and copper re-piping services.',
      icon: '🔧'
    },
    {
      title: 'Bathroom Remodeling',
      description: 'Complete bathroom renovations with expert plumbing installation.',
      icon: '🛁'
    },
    {
      title: 'Kitchen Remodeling',
      description: 'Kitchen plumbing for sinks, dishwashers, and garbage disposals.',
      icon: '🍽️'
    }
  ];

  const features = [
    {
      icon: <Clock className="h-8 w-8 text-[var(--qd-gold)]" />,
      title: '24/7 Emergency Service',
      description: 'Available around the clock for urgent plumbing emergencies'
    },
    {
      icon: <Shield className="h-8 w-8 text-[var(--qd-gold)]" />,
      title: 'Licensed & Insured',
      description: 'Fully licensed and insured for your peace of mind'
    },
    {
      icon: <Users className="h-8 w-8 text-[var(--qd-gold)]" />,
      title: '500+ Satisfied Customers',
      description: 'Over a decade of trusted service in Greater Boston'
    },
    {
      icon: <CheckCircle className="h-8 w-8 text-[var(--qd-gold)]" />,
      title: 'Quality Guaranteed',
      description: 'All work backed by comprehensive warranties'
    }
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      location: 'Brockton, MA',
      rating: 5,
      text: 'Eric and his team were incredibly professional and efficient. They fixed our water heater issue quickly and explained everything clearly.'
    },
    {
      name: 'Mike Chen',
      location: 'Bridgewater, MA',
      rating: 5,
      text: 'Quality Deluxe saved the day when our basement flooded. They responded immediately and handled everything professionally.'
    },
    {
      name: 'Lisa Rodriguez',
      location: 'Whitman, MA',
      rating: 5,
      text: 'Outstanding service for our bathroom remodel. The team was punctual, clean, and delivered exactly what they promised.'
    }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[var(--qd-navy)] to-[var(--qd-light-blue)] text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Professional Plumbing Services in{' '}
              <span className="text-[var(--qd-gold)]">Brockton, MA</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200">
              Trusted plumbing experts serving Greater Boston for over 10 years. 
              24/7 emergency services, expert installations, and complete remodeling solutions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="bg-[var(--qd-orange)] hover:bg-[var(--qd-orange)]/90 text-white text-lg px-8 py-4"
                asChild
              >
                <a href="tel:5083152109" className="flex items-center space-x-2">
                  <Phone className="h-5 w-5" />
                  <span>Call (*************</span>
                </a>
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="border-white text-white hover:bg-white hover:text-[var(--qd-navy)] text-lg px-8 py-4"
                asChild
              >
                <a href="/contact">Get Free Estimate</a>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold text-[var(--qd-navy)] mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-4">
              Our Comprehensive Services
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              From emergency repairs to complete remodeling, we provide all the plumbing services 
              you need for your home or business.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-semibold text-[var(--qd-navy)] mb-3">
                    {service.title}
                  </h3>
                  <p className="text-gray-600">{service.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Button 
              size="lg" 
              className="bg-[var(--qd-navy)] hover:bg-[var(--qd-navy)]/90 text-white"
              asChild
            >
              <a href="/services">View All Services</a>
            </Button>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-6">
                Why Choose Quality Deluxe?
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                At Quality Deluxe LLC, we understand that plumbing issues can disrupt your daily life. 
                That's why we've dedicated over 10 years to providing exceptional plumbing services 
                to homeowners and businesses throughout Brockton, Massachusetts, and the Greater Boston area.
              </p>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)]">Over 10 Years of Experience</h4>
                    <p className="text-gray-600">Extensive experience means quick diagnosis and effective solutions</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)]">24/7 Emergency Response</h4>
                    <p className="text-gray-600">Available around the clock for urgent plumbing emergencies</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)]">Licensed and Insured</h4>
                    <p className="text-gray-600">All technicians fully licensed with comprehensive insurance coverage</p>
                  </div>
                </div>
              </div>
              <div className="mt-8">
                <Button 
                  size="lg" 
                  className="bg-[var(--qd-navy)] hover:bg-[var(--qd-navy)]/90 text-white"
                  asChild
                >
                  <a href="/about">Learn More About Us</a>
                </Button>
              </div>
            </div>
            <div className="bg-[var(--qd-navy)] text-white p-8 rounded-lg">
              <h3 className="text-2xl font-bold mb-6 text-[var(--qd-gold)]">Service Areas</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <ul className="space-y-2">
                    <li>Brockton, MA</li>
                    <li>Abington, MA</li>
                    <li>Bridgewater, MA</li>
                    <li>East Bridgewater, MA</li>
                    <li>West Bridgewater, MA</li>
                  </ul>
                </div>
                <div>
                  <ul className="space-y-2">
                    <li>Whitman, MA</li>
                    <li>Hanson, MA</li>
                    <li>Halifax, MA</li>
                    <li>Rockland, MA</li>
                    <li>Holbrook, MA</li>
                  </ul>
                </div>
              </div>
              <p className="mt-4 text-gray-300">
                Serving Greater Boston and surrounding communities
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-4">
              What Our Customers Say
            </h2>
            <p className="text-lg text-gray-600">
              Over 500 satisfied customers trust Quality Deluxe for their plumbing needs
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-[var(--qd-gold)] fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-4 italic">"{testimonial.text}"</p>
                  <div>
                    <p className="font-semibold text-[var(--qd-navy)]">{testimonial.name}</p>
                    <p className="text-sm text-gray-500">{testimonial.location}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-[var(--qd-navy)] text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Experience the Quality Deluxe Difference?
          </h2>
          <p className="text-xl mb-8 text-gray-200">
            Contact us today for emergency service or to schedule your free estimate
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-[var(--qd-orange)] hover:bg-[var(--qd-orange)]/90 text-white text-lg px-8 py-4"
              asChild
            >
              <a href="tel:5083152109" className="flex items-center space-x-2">
                <Phone className="h-5 w-5" />
                <span>Call Now: (*************</span>
              </a>
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="border-white text-white hover:bg-white hover:text-[var(--qd-navy)] text-lg px-8 py-4"
              asChild
            >
              <a href="/contact">Get Free Estimate</a>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;

