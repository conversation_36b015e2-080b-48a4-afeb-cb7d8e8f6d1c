import { Phone, Clock, CheckCircle, Wrench, Droplets, Flame, Zap, Home, Building } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';

const Services = () => {
  const emergencyServices = [
    {
      icon: <Droplets className="h-8 w-8 text-[var(--qd-orange)]" />,
      title: 'Burst Pipe Repair',
      description: 'Immediate response to burst pipes with tools and materials to stop leaks and restore your system.'
    },
    {
      icon: <Wrench className="h-8 w-8 text-[var(--qd-orange)]" />,
      title: 'Severe Leak Detection',
      description: 'Advanced leak detection equipment to locate hidden leaks and provide effective repairs.'
    },
    {
      icon: <Building className="h-8 w-8 text-[var(--qd-orange)]" />,
      title: 'Drain & Sewer Backups',
      description: 'Professional-grade equipment to clear blockages and restore proper drainage.'
    },
    {
      icon: <Flame className="h-8 w-8 text-[var(--qd-orange)]" />,
      title: 'Water Heater Failures',
      description: 'Emergency water heater repair and replacement to restore your hot water supply.'
    }
  ];

  const mainServices = [
    {
      category: 'Plumbing Repair & Installation',
      icon: <Wrench className="h-12 w-12 text-[var(--qd-gold)]" />,
      services: [
        'Faucet and fixture installation',
        'Pipe repair and replacement',
        'Toilet repair and installation',
        'Garbage disposal services',
        'Shut-off valve installation',
        'Copper re-piping'
      ]
    },
    {
      category: 'Water Heater Services',
      icon: <Flame className="h-12 w-12 text-[var(--qd-gold)]" />,
      services: [
        'Traditional tank water heaters',
        'Tankless water heater installation',
        'Water heater maintenance',
        'Water heater repair',
        'Energy efficiency upgrades',
        'Water heater replacement'
      ]
    },
    {
      category: 'Drain & Sewer Services',
      icon: <Droplets className="h-12 w-12 text-[var(--qd-gold)]" />,
      services: [
        'Professional drain cleaning',
        'Sewer camera inspection',
        'Trenchless sewer repair',
        'Slab leak detection',
        'Hydro-jetting services',
        'Root removal'
      ]
    },
    {
      category: 'Gas Line Services',
      icon: <Zap className="h-12 w-12 text-[var(--qd-gold)]" />,
      services: [
        'Gas line installation',
        'Gas line repair',
        'Gas line replacement',
        'Gas appliance connections',
        'Gas leak detection',
        'Safety inspections'
      ]
    },
    {
      category: 'Kitchen Remodeling',
      icon: <Home className="h-12 w-12 text-[var(--qd-gold)]" />,
      services: [
        'Kitchen sink installation',
        'Dishwasher connections',
        'Garbage disposal installation',
        'Water line extensions',
        'Ice maker connections',
        'Custom plumbing design'
      ]
    },
    {
      category: 'Bathroom Remodeling',
      icon: <Building className="h-12 w-12 text-[var(--qd-gold)]" />,
      services: [
        'Complete bathroom renovations',
        'Fixture installation',
        'Shower and tub installation',
        'Vanity plumbing',
        'Tile work coordination',
        'ADA compliance upgrades'
      ]
    }
  ];

  const processSteps = [
    {
      step: '1',
      title: 'Contact Us',
      description: 'Call us or fill out our online form to describe your plumbing needs'
    },
    {
      step: '2',
      title: 'Assessment',
      description: 'Our expert technician evaluates the situation and provides a detailed estimate'
    },
    {
      step: '3',
      title: 'Work Completion',
      description: 'We complete the work efficiently using quality materials and proven techniques'
    },
    {
      step: '4',
      title: 'Follow-up',
      description: 'We ensure your satisfaction and provide ongoing support and warranties'
    }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[var(--qd-navy)] to-[var(--qd-light-blue)] text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Comprehensive <span className="text-[var(--qd-gold)]">Plumbing Services</span>
            </h1>
            <p className="text-xl text-gray-200 mb-8">
              From emergency repairs to complete remodeling, we provide all the plumbing services 
              you need for your home or business in Brockton, MA and Greater Boston.
            </p>
            <Button 
              size="lg" 
              className="bg-[var(--qd-orange)] hover:bg-[var(--qd-orange)]/90 text-white text-lg px-8 py-4"
              asChild
            >
              <a href="tel:5083152109" className="flex items-center space-x-2">
                <Phone className="h-5 w-5" />
                <span>Call (*************</span>
              </a>
            </Button>
          </div>
        </div>
      </section>

      {/* Emergency Services */}
      <section className="py-16 bg-red-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Clock className="h-8 w-8 text-[var(--qd-orange)]" />
              <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)]">
                24/7 Emergency Services
              </h2>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Plumbing emergencies can occur at any time. Our emergency response team is available 
              around the clock to address urgent issues that can't wait.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {emergencyServices.map((service, index) => (
              <Card key={index} className="border-2 border-[var(--qd-orange)] hover:shadow-lg transition-shadow">
                <CardContent className="p-6 text-center">
                  <div className="flex justify-center mb-4">
                    {service.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-[var(--qd-navy)] mb-3">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 text-sm">{service.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center mt-8">
            <div className="bg-[var(--qd-orange)] text-white p-4 rounded-lg inline-block">
              <p className="font-semibold text-lg">Emergency Hotline: (*************</p>
              <p className="text-sm">Available 24 hours a day, 7 days a week</p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Services */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-4">
              Our Complete Service Offerings
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We provide a comprehensive range of plumbing services designed to meet all your 
              residential and commercial needs.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {mainServices.map((service, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow h-full">
                <CardHeader className="text-center pb-4">
                  <div className="flex justify-center mb-4">
                    {service.icon}
                  </div>
                  <CardTitle className="text-xl text-[var(--qd-navy)]">
                    {service.category}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {service.services.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start space-x-2">
                        <CheckCircle className="h-4 w-4 text-[var(--qd-gold)] mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600 text-sm">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-4">
              Our Service Process
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We follow a proven process to ensure quality results and customer satisfaction on every project.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="bg-[var(--qd-navy)] text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                  {step.step}
                </div>
                <h3 className="text-xl font-semibold text-[var(--qd-navy)] mb-3">
                  {step.title}
                </h3>
                <p className="text-gray-600">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Commercial Services */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-4">
                Commercial Plumbing Services
              </h2>
              <p className="text-lg text-gray-600">
                Quality Deluxe also provides comprehensive commercial plumbing services for businesses, 
                restaurants, office buildings, and other commercial properties.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <Building className="h-12 w-12 text-[var(--qd-gold)] mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-[var(--qd-navy)] mb-3">
                    Commercial Repairs
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Minimize downtime with quick, professional repairs that get your business back to normal operations.
                  </p>
                </CardContent>
              </Card>
              
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <Wrench className="h-12 w-12 text-[var(--qd-gold)] mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-[var(--qd-navy)] mb-3">
                    Installation Services
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Complete commercial plumbing installation for new construction and tenant improvements.
                  </p>
                </CardContent>
              </Card>
              
              <Card className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <Clock className="h-12 w-12 text-[var(--qd-gold)] mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-[var(--qd-navy)] mb-3">
                    Maintenance Contracts
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Prevent costly emergencies with regular maintenance contracts tailored to your business needs.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-8 text-center">
              Why Choose Quality Deluxe?
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)] mb-2">Experienced Professionals</h4>
                    <p className="text-gray-600">Over 10 years in the industry with the knowledge to handle any challenge.</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)] mb-2">Quality Materials</h4>
                    <p className="text-gray-600">We use only high-quality materials and components for lasting results.</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)] mb-2">Upfront Pricing</h4>
                    <p className="text-gray-600">Clear, detailed estimates with no hidden fees or surprises.</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)] mb-2">Guaranteed Work</h4>
                    <p className="text-gray-600">All work backed by comprehensive warranties for your peace of mind.</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)] mb-2">Emergency Availability</h4>
                    <p className="text-gray-600">24/7 emergency service means help is always available when needed.</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)] mb-2">Local Expertise</h4>
                    <p className="text-gray-600">Deep understanding of local codes and unique regional challenges.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-[var(--qd-navy)] text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl mb-8 text-gray-200">
            Contact Quality Deluxe today to learn more about our comprehensive plumbing services 
            and to schedule your appointment.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-[var(--qd-orange)] hover:bg-[var(--qd-orange)]/90 text-white text-lg px-8 py-4"
              asChild
            >
              <a href="tel:5083152109" className="flex items-center space-x-2">
                <Phone className="h-5 w-5" />
                <span>Call (*************</span>
              </a>
            </Button>
            <Button 
              size="lg" 
              variant="outline" 
              className="border-white text-white hover:bg-white hover:text-[var(--qd-navy)] text-lg px-8 py-4"
              asChild
            >
              <a href="/contact">Get Free Estimate</a>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Services;

