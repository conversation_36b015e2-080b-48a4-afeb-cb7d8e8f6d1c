import { useState } from 'react';
import { Phone, MapPin, Clock, Mail, Send } from 'lucide-react';
import { But<PERSON> } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Textarea } from '../components/ui/textarea';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: '',
    message: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // In a real application, you would send this data to your backend
    console.log('Form submitted:', formData);
    alert('Thank you for your message! We will contact you soon.');
    setFormData({
      name: '',
      email: '',
      phone: '',
      service: '',
      message: ''
    });
  };

  const serviceAreas = [
    'Brockton, MA',
    'Abington, MA',
    'Bridgewater, MA',
    'East Bridgewater, MA',
    'West Bridgewater, MA',
    'Whitman, MA',
    'Hanson, MA',
    'Halifax, MA',
    'Rockland, MA',
    'Holbrook, MA'
  ];

  const contactMethods = [
    {
      icon: <Phone className="h-8 w-8 text-[var(--qd-gold)]" />,
      title: 'Phone',
      primary: '(*************',
      secondary: 'Available 24/7 for emergencies',
      action: 'tel:5083152109'
    },
    {
      icon: <Mail className="h-8 w-8 text-[var(--qd-gold)]" />,
      title: 'Email',
      primary: '<EMAIL>',
      secondary: 'We respond within 24 hours',
      action: 'mailto:<EMAIL>'
    },
    {
      icon: <MapPin className="h-8 w-8 text-[var(--qd-gold)]" />,
      title: 'Address',
      primary: '623 W Chestnut Street',
      secondary: 'Brockton, MA 02301',
      action: 'https://maps.google.com/?q=623+W+Chestnut+St,+Brockton,+MA+02301'
    }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[var(--qd-navy)] to-[var(--qd-light-blue)] text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Contact <span className="text-[var(--qd-gold)]">Quality Deluxe</span>
            </h1>
            <p className="text-xl text-gray-200 mb-8">
              Get in touch with our professional plumbing team. We're here to help with all your 
              plumbing needs in Brockton, MA and Greater Boston.
            </p>
            <Button 
              size="lg" 
              className="bg-[var(--qd-orange)] hover:bg-[var(--qd-orange)]/90 text-white text-lg px-8 py-4"
              asChild
            >
              <a href="tel:5083152109" className="flex items-center space-x-2">
                <Phone className="h-5 w-5" />
                <span>Call Now: (*************</span>
              </a>
            </Button>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-4">
              Get in Touch
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Whether you need emergency service or want to schedule routine maintenance, 
              we're here to help. Choose the contact method that works best for you.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {contactMethods.map((method, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex justify-center mb-4">
                    {method.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-[var(--qd-navy)] mb-3">
                    {method.title}
                  </h3>
                  <p className="text-lg font-medium text-gray-800 mb-2">
                    {method.primary}
                  </p>
                  <p className="text-gray-600 mb-4">
                    {method.secondary}
                  </p>
                  <Button 
                    className="bg-[var(--qd-navy)] hover:bg-[var(--qd-navy)]/90 text-white"
                    asChild
                  >
                    <a href={method.action} target={method.title === 'Address' ? '_blank' : '_self'}>
                      {method.title === 'Phone' ? 'Call Now' : 
                       method.title === 'Email' ? 'Send Email' : 'Get Directions'}
                    </a>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form & Business Hours */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl text-[var(--qd-navy)]">
                    Request a Free Estimate
                  </CardTitle>
                  <p className="text-gray-600">
                    Fill out the form below and we'll get back to you within 24 hours.
                  </p>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                          Full Name *
                        </label>
                        <Input
                          id="name"
                          name="name"
                          type="text"
                          required
                          value={formData.name}
                          onChange={handleInputChange}
                          placeholder="Your full name"
                        />
                      </div>
                      <div>
                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                          Phone Number *
                        </label>
                        <Input
                          id="phone"
                          name="phone"
                          type="tel"
                          required
                          value={formData.phone}
                          onChange={handleInputChange}
                          placeholder="(*************"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-1">
                        Service Needed
                      </label>
                      <select
                        id="service"
                        name="service"
                        value={formData.service}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--qd-navy)] focus:border-transparent"
                      >
                        <option value="">Select a service</option>
                        <option value="emergency">Emergency Repair</option>
                        <option value="water-heater">Water Heater Service</option>
                        <option value="drain-cleaning">Drain Cleaning</option>
                        <option value="pipe-repair">Pipe Repair/Installation</option>
                        <option value="bathroom-remodel">Bathroom Remodeling</option>
                        <option value="kitchen-remodel">Kitchen Remodeling</option>
                        <option value="maintenance">Routine Maintenance</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                        Message *
                      </label>
                      <Textarea
                        id="message"
                        name="message"
                        required
                        value={formData.message}
                        onChange={handleInputChange}
                        placeholder="Please describe your plumbing needs or any questions you have..."
                        rows={4}
                      />
                    </div>
                    
                    <Button 
                      type="submit" 
                      className="w-full bg-[var(--qd-navy)] hover:bg-[var(--qd-navy)]/90 text-white"
                    >
                      <Send className="h-4 w-4 mr-2" />
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Business Hours & Info */}
            <div className="space-y-8">
              {/* Business Hours */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl text-[var(--qd-navy)] flex items-center">
                    <Clock className="h-6 w-6 mr-2 text-[var(--qd-gold)]" />
                    Business Hours
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center py-2 border-b border-gray-200">
                      <span className="font-medium">Monday - Friday</span>
                      <span className="text-gray-600">7:00 AM - 6:00 PM</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-200">
                      <span className="font-medium">Saturday</span>
                      <span className="text-gray-600">8:00 AM - 4:00 PM</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-200">
                      <span className="font-medium">Sunday</span>
                      <span className="text-gray-600">Emergency Service Only</span>
                    </div>
                    <div className="bg-[var(--qd-orange)] text-white p-4 rounded-lg mt-4">
                      <p className="font-semibold text-center">
                        24/7 Emergency Service Available
                      </p>
                      <p className="text-center text-sm mt-1">
                        Call (************* for urgent plumbing issues
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Service Areas */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl text-[var(--qd-navy)] flex items-center">
                    <MapPin className="h-6 w-6 mr-2 text-[var(--qd-gold)]" />
                    Service Areas
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">
                    Quality Deluxe proudly serves Brockton and the surrounding Greater Boston communities:
                  </p>
                  <div className="grid grid-cols-2 gap-2">
                    {serviceAreas.map((area, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-[var(--qd-gold)] rounded-full"></div>
                        <span className="text-sm text-gray-700">{area}</span>
                      </div>
                    ))}
                  </div>
                  <p className="text-gray-600 mt-4 text-sm">
                    Not sure if we service your area? Call us at (************* to confirm availability.
                  </p>
                </CardContent>
              </Card>

              {/* Payment Options */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl text-[var(--qd-navy)]">
                    Payment Options
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">
                    We accept various payment methods for your convenience:
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-[var(--qd-gold)] rounded-full"></div>
                      <span className="text-gray-700">Cash</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-[var(--qd-gold)] rounded-full"></div>
                      <span className="text-gray-700">Personal checks</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-[var(--qd-gold)] rounded-full"></div>
                      <span className="text-gray-700">Major credit cards</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-[var(--qd-gold)] rounded-full"></div>
                      <span className="text-gray-700">Financing options available</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Emergency Banner */}
      <section className="py-12 bg-[var(--qd-navy)] text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-4">
            Plumbing Emergency?
          </h2>
          <p className="text-lg mb-6 text-gray-200">
            Don't wait! Our emergency response team is available 24/7 to help with urgent plumbing issues.
          </p>
          <Button 
            size="lg" 
            className="bg-[var(--qd-orange)] hover:bg-[var(--qd-orange)]/90 text-white text-xl px-10 py-5"
            asChild
          >
            <a href="tel:5083152109" className="flex items-center space-x-2">
              <Phone className="h-6 w-6" />
              <span>Call Emergency Line: (*************</span>
            </a>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default Contact;

