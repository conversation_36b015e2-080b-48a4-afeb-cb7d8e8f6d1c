import { CheckCircle, Users, Award, Clock, Shield } from 'lucide-react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';

const About = () => {
  const values = [
    {
      icon: <Award className="h-8 w-8 text-[var(--qd-gold)]" />,
      title: 'Quality Workmanship',
      description: 'We take pride in every job, using only the finest materials and techniques to ensure lasting results.'
    },
    {
      icon: <Users className="h-8 w-8 text-[var(--qd-gold)]" />,
      title: 'Customer Service Excellence',
      description: 'Your satisfaction is our top priority. We listen, communicate clearly, and stand behind our work.'
    },
    {
      icon: <Shield className="h-8 w-8 text-[var(--qd-gold)]" />,
      title: 'Integrity and Honesty',
      description: 'Transparent pricing, honest assessments, and ethical business practices you can trust.'
    },
    {
      icon: <Clock className="h-8 w-8 text-[var(--qd-gold)]" />,
      title: 'Continuous Improvement',
      description: 'We stay current with the latest technologies and techniques to provide the best solutions.'
    }
  ];

  const stats = [
    { number: '10+', label: 'Years of Experience' },
    { number: '500+', label: 'Satisfied Customers' },
    { number: '24/7', label: 'Emergency Service' },
    { number: '100%', label: 'Licensed & Insured' }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[var(--qd-navy)] to-[var(--qd-light-blue)] text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              About <span className="text-[var(--qd-gold)]">Quality Deluxe</span>
            </h1>
            <p className="text-xl text-gray-200">
              Professional plumbing experts serving Brockton, MA and Greater Boston for over a decade
            </p>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-[var(--qd-navy)] mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-8 text-center">
              Our Story
            </h2>
            <div className="prose prose-lg max-w-none text-gray-600">
              <p className="text-lg leading-relaxed mb-6">
                Quality Deluxe LLC was founded with a simple yet powerful mission: to provide exceptional 
                plumbing services that exceed customer expectations while building lasting relationships 
                within our community. For over a decade, we have been the trusted choice for homeowners 
                and businesses throughout Brockton, Massachusetts, and the Greater Boston area.
              </p>
              <p className="text-lg leading-relaxed mb-6">
                Our journey began with a commitment to combining traditional craftsmanship with modern 
                technology and techniques. We recognized that the plumbing industry needed professionals 
                who could not only solve immediate problems but also provide comprehensive solutions that 
                prevent future issues. This philosophy has guided our growth from a small local operation 
                to one of the most respected plumbing companies in the region.
              </p>
              <p className="text-lg leading-relaxed">
                Today, with more than 500 satisfied customers and countless successful projects, we continue 
                to uphold the values that have made us successful: quality workmanship, exceptional customer 
                service, and unwavering integrity in everything we do.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-8">
              Our Mission
            </h2>
            <div className="bg-white p-8 rounded-lg shadow-lg">
              <p className="text-xl text-gray-700 leading-relaxed">
                At Quality Deluxe, we believe that exceptional service and exceptional quality go hand in hand. 
                Our mission is to provide comprehensive plumbing solutions that enhance the comfort, safety, 
                and value of our customers' properties while maintaining the highest standards of professionalism 
                and integrity.
              </p>
              <div className="mt-6 text-lg text-[var(--qd-navy)] font-semibold">
                "Always there for you" - providing reliable service when you need it most.
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-4">
              Our Values
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              These core values guide everything we do and shape how we serve our customers
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      {value.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-[var(--qd-navy)] mb-3">
                        {value.title}
                      </h3>
                      <p className="text-gray-600">{value.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-8 text-center">
              Our Team
            </h2>
            <div className="text-lg text-gray-600 space-y-6">
              <p>
                Our success is built on the expertise and dedication of our skilled team of plumbing 
                professionals. Each member of our team brings years of experience and specialized 
                knowledge to every project. We invest heavily in ongoing training and certification 
                to ensure our technicians stay current with the latest industry developments and best practices.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
                <div className="text-center">
                  <div className="bg-[var(--qd-navy)] text-white p-6 rounded-lg">
                    <CheckCircle className="h-12 w-12 text-[var(--qd-gold)] mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Licensed Professionals</h3>
                    <p className="text-sm text-gray-300">
                      All plumbers fully licensed by the Commonwealth of Massachusetts
                    </p>
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="bg-[var(--qd-navy)] text-white p-6 rounded-lg">
                    <Award className="h-12 w-12 text-[var(--qd-gold)] mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Experienced Specialists</h3>
                    <p className="text-sm text-gray-300">
                      Specialists in emergency repairs, installations, and remodeling
                    </p>
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="bg-[var(--qd-navy)] text-white p-6 rounded-lg">
                    <Users className="h-12 w-12 text-[var(--qd-gold)] mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Customer-Focused</h3>
                    <p className="text-sm text-gray-300">
                      Selected for technical skills and commitment to service
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Experience Matters */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-8 text-center">
              Why Experience Matters
            </h2>
            <p className="text-lg text-gray-600 mb-8 text-center">
              With over 10 years in the plumbing industry and more than 500 satisfied customers, 
              we've encountered virtually every type of plumbing challenge.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)]">Quick Diagnosis</h4>
                    <p className="text-gray-600">Quickly identify problems and the most effective solutions</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)]">Preventive Approach</h4>
                    <p className="text-gray-600">Anticipate potential issues before they become major problems</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)]">Value-Added Recommendations</h4>
                    <p className="text-gray-600">Suggest upgrades that add value to your property</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)]">Efficient Work</h4>
                    <p className="text-gray-600">Work efficiently to minimize disruption and reduce costs</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)]">Accurate Estimates</h4>
                    <p className="text-gray-600">Provide accurate estimates and realistic timelines</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-[var(--qd-gold)] mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-[var(--qd-navy)]">Local Expertise</h4>
                    <p className="text-gray-600">Deep understanding of local building codes and challenges</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Community Involvement */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--qd-navy)] mb-8">
              Community Involvement
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              As a locally owned and operated business, Quality Deluxe is committed to giving back 
              to the Brockton community that has supported our growth and success. We participate 
              in local charity events, support community organizations, and provide emergency services 
              to help our neighbors during times of crisis.
            </p>
            <p className="text-lg text-gray-600">
              Our deep roots in the community mean we understand the unique challenges faced by 
              property owners in our area, from older plumbing systems in historic homes to the 
              specific requirements of local building codes and regulations.
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-[var(--qd-navy)] text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Experience the Quality Deluxe Difference
          </h2>
          <p className="text-xl mb-8 text-gray-200">
            Whether you need emergency repairs, routine maintenance, or planning a major renovation, 
            we have the experience and commitment to excellence you can trust.
          </p>
          <Button 
            size="lg" 
            className="bg-[var(--qd-orange)] hover:bg-[var(--qd-orange)]/90 text-white text-lg px-8 py-4"
            asChild
          >
            <a href="/contact">Contact Us Today</a>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default About;

