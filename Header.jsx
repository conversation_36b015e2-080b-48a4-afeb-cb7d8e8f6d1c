import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Phone, Menu, X } from 'lucide-react';
import { Button } from './ui/button';
import logo from '../assets/quality-deluxe-logo.png';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { name: 'Services', href: '/services' },
    { name: 'Contact', href: '/contact' },
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      {/* Emergency Banner */}
      <div className="bg-[var(--qd-orange)] text-white py-2">
        <div className="container mx-auto px-4 text-center">
          <span className="font-semibold">24/7 Emergency Service Available</span>
          <a href="tel:5083152109" className="ml-4 hover:underline font-bold">
            Call (*************
          </a>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between py-4">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <img src={logo} alt="Quality Deluxe LLC" className="h-12 w-12" />
            <div>
              <h1 className="text-xl font-bold text-[var(--qd-navy)]">Quality Deluxe</h1>
              <p className="text-sm text-[var(--qd-gray)]">Professional Plumbing Services</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`font-medium transition-colors hover:text-[var(--qd-gold)] ${
                  isActive(item.href) 
                    ? 'text-[var(--qd-gold)] border-b-2 border-[var(--qd-gold)]' 
                    : 'text-[var(--qd-navy)]'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Contact Button & Mobile Menu */}
          <div className="flex items-center space-x-4">
            <Button 
              asChild
              className="hidden sm:flex bg-[var(--qd-navy)] hover:bg-[var(--qd-navy)]/90 text-white"
            >
              <a href="tel:5083152109" className="flex items-center space-x-2">
                <Phone className="h-4 w-4" />
                <span>(*************</span>
              </a>
            </Button>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2 text-[var(--qd-navy)]"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <nav className="flex flex-col space-y-4">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`font-medium transition-colors hover:text-[var(--qd-gold)] ${
                    isActive(item.href) ? 'text-[var(--qd-gold)]' : 'text-[var(--qd-navy)]'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              <Button 
                asChild
                className="bg-[var(--qd-navy)] hover:bg-[var(--qd-navy)]/90 text-white w-fit"
              >
                <a href="tel:5083152109" className="flex items-center space-x-2">
                  <Phone className="h-4 w-4" />
                  <span>(*************</span>
                </a>
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;

