import { Link } from 'react-router-dom';
import { Phone, MapPin, Clock, Mail, Facebook } from 'lucide-react';
import logo from '../assets/quality-deluxe-logo.png';

const Footer = () => {
  return (
    <footer className="bg-[var(--qd-navy)] text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <div className="flex items-center space-x-3 mb-4">
              <img src={logo} alt="Quality Deluxe LLC" className="h-10 w-10" />
              <div>
                <h3 className="text-lg font-bold">Quality Deluxe</h3>
                <p className="text-sm text-gray-300">Professional Plumbing</p>
              </div>
            </div>
            <p className="text-gray-300 mb-4">
              Over 10 years of trusted plumbing services in Brockton, MA and Greater Boston area.
            </p>
            <div className="flex space-x-4">
              <a 
                href="https://m.facebook.com/qualitydeluxe/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-[var(--qd-gold)] transition-colors"
              >
                <Facebook className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-[var(--qd-gold)] transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-300 hover:text-[var(--qd-gold)] transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/services" className="text-gray-300 hover:text-[var(--qd-gold)] transition-colors">
                  Services
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-300 hover:text-[var(--qd-gold)] transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Our Services</h3>
            <ul className="space-y-2 text-gray-300">
              <li>Emergency Plumbing</li>
              <li>Water Heater Services</li>
              <li>Drain Cleaning</li>
              <li>Pipe Repair & Installation</li>
              <li>Bathroom Remodeling</li>
              <li>Kitchen Remodeling</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Info</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-[var(--qd-gold)] mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-gray-300">623 W Chestnut St</p>
                  <p className="text-gray-300">Brockton, MA 02301</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-[var(--qd-gold)]" />
                <a href="tel:5083152109" className="text-gray-300 hover:text-[var(--qd-gold)] transition-colors">
                  (*************
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-[var(--qd-gold)]" />
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-[var(--qd-gold)] transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-start space-x-3">
                <Clock className="h-5 w-5 text-[var(--qd-gold)] mt-0.5" />
                <div className="text-gray-300">
                  <p>Mon-Fri: 7AM-6PM</p>
                  <p>Sat: 8AM-4PM</p>
                  <p>24/7 Emergency Service</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-8 pt-8 text-center">
          <p className="text-gray-300">
            © 2024 Quality Deluxe LLC. All rights reserved. | Licensed & Insured Plumbing Contractor
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

